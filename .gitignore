# Augment Code AI Assistant
.augment/
.augment/rules/
.augment/cache/
.augment/logs/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# PHP
*.log
*.cache
vendor/
composer.lock

# XAMPP specific
.htaccess.bak
error_log

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Environment files
.env
.env.local
.env.production
.env.staging

# Node modules (if using any frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
public/build/

# Cache directories
.cache/
.parcel-cache/

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Project specific
storage/
uploads/
assets/uploads/
